<template>
  <cm-container @onLoad="onLoad">
    <div
      class="ssw-message"
      element-loading-text="加载中..."
      v-loading="loading"
      :element-loading-svg="svg"
      element-loading-background="rgba(255, 255, 255, 0.5)"
      element-loading-svg-view-box="-10, -10, 50, 50">
      <div class="ssw-message-search">
        <div class="ssw-message-search-item">
          <el-date-picker
            style="width: 200px;"
            v-model="searchParams.startDate"
            type="date"
            placeholder="请选择日期" />
          <el-select v-model="searchParams.type" placeholder="请选择消息类型"
            style="width: 200px">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </div>
        <div class="ssw-message-search-btn">
          <el-button type="primary" @click="onSearch"> 搜索 </el-button>
          <el-button @click="onClear">清空</el-button>
        </div>
      </div>
      <div class="message-operations"
        v-if="hasAuth('message:readAll')">
        <el-button
          type="primary"
          size="small">全部标记已读</el-button>
      </div>
      <div class="message-table" ref="tableRef">
        <el-table :data="tableData" style="width: 100%"
          :height="tableHeight"
          show-overflow-tooltip>
          <el-table-column type="index" label="序号" width="55"
            align="center" />
          <el-table-column prop="prop" label="消息类型" width="150"
            align="center" />
          <el-table-column prop="prop" label="消息内容" align="center" />
          <el-table-column prop="prop" label="发送时间" width="180"
            align="center" />
          <el-table-column prop="prop" label="接收人" width="100"
            align="center" />
          <el-table-column prop="prop" label="是否已读" width="100"
            align="center" />
          <el-table-column prop="prop" label="操作" width="80"
            align="center" v-if="hasAuth('message:view')">
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="onView(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="message-pagination">
        <el-pagination v-model:current-page="pageParams.current"
          v-model:page-size="pageParams.size" background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageParams.total" @change="onLoad" />
      </div>
    </div>

  </cm-container>
</template>

<script setup>
import CmContainer from '@/components/cm-container/main.vue'
import { useResizeObserver } from '@/hooks/useResizeObserver'
import { hasAuth } from '@/utils/auth'
import { onMounted } from 'vue'
const loading = ref(true)
const searchParams = reactive({
  startDate: '',
  type: ''
})
const pageParams = reactive({
  current: 1,
  size: 10,
  total: 0
})
const svg = `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `
const tableRef = useTemplateRef('tableRef')
const tableHeight = ref(0)
onMounted(() => {
  useResizeObserver(tableRef, entries => {
    const entry = entries[0]
    const { height: _h } = entry.contentRect
    tableHeight.value = _h - 2
  })
})
const tableData = ref([])
const onLoad = () => {
  loading.value = false
}
const onSearch = () => {
  console.log('onSearch')
}
const onClear = () => {
  console.log('onClear')
}
const onView = (row) => {
  console.log('onView', row)
}
</script>

<style lang="scss" scoped>
.ssw-message {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  box-sizing: border-box;
  padding: 12px;
  gap: 12px;

  .ssw-message-search {
    display: flex;
    align-items: center;
    column-gap: 12px;

    .ssw-message-search-item {
      @extend .ssw-message-search;
    }
  }

  .message-table {
    flex: 1;
    overflow: hidden;
  }

  .message-pagination {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
