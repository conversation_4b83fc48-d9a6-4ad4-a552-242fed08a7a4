/**
 * hasAuth 方法性能优化测试
 */

// Mock store
const mockStore = {
  getters: {
    permission: {
      'user:add': true,
      'user:edit': true,
      'user:delete': true,
      'admin:manage': true
    }
  }
}

// Mock 模块
jest.mock('@/store', () => mockStore)

// 导入要测试的函数
import { hasAuth } from '../auth'

describe('hasAuth 性能优化测试', () => {

  test('单个权限码字符串验证', () => {
    expect(hasAuth('user:add')).toBe(true)
    expect(hasAuth('user:view')).toBe(false)
    expect(hasAuth('admin:manage')).toBe(true)
  })

  test('权限码数组验证（OR逻辑）', () => {
    expect(hasAuth(['user:add', 'user:edit'])).toBe(true)
    expect(hasAuth(['user:view', 'user:export'])).toBe(false)
    expect(hasAuth(['user:view', 'user:add'])).toBe(true)
  })

  test('参数验证', () => {
    expect(hasAuth(null)).toBe(false)
    expect(hasAuth(undefined)).toBe(false)
    expect(hasAuth('')).toBe(false)
    expect(hasAuth([])).toBe(false)
  })

  test('不支持的参数类型', () => {
    expect(hasAuth(123)).toBe(false)
    expect(hasAuth({})).toBe(false)
    expect(hasAuth(true)).toBe(false)
  })

  test('缓存机制测试', () => {
    // 第一次调用
    const start1 = performance.now()
    hasAuth('user:add')
    const end1 = performance.now()
    const time1 = end1 - start1

    // 第二次调用（应该使用缓存）
    const start2 = performance.now()
    hasAuth('user:edit')
    const end2 = performance.now()
    const time2 = end2 - start2

    // 缓存调用应该更快（虽然在测试环境中差异可能很小）
    console.log(`第一次调用时间: ${time1}ms, 第二次调用时间: ${time2}ms`)
  })

  test('性能基准测试', () => {
    const iterations = 10000

    // 测试单个权限码性能
    const start = performance.now()
    for (let i = 0; i < iterations; i++) {
      hasAuth('user:add')
    }
    const end = performance.now()

    const avgTime = (end - start) / iterations
    console.log(`平均每次调用时间: ${avgTime}ms`)

    // 期望每次调用时间小于 0.01ms（在现代浏览器中）
    expect(avgTime).toBeLessThan(0.01)
  })

  test('数组权限性能测试', () => {
    const iterations = 5000
    const permissions = ['user:view', 'user:export', 'user:add'] // 最后一个有权限

    const start = performance.now()
    for (let i = 0; i < iterations; i++) {
      hasAuth(permissions)
    }
    const end = performance.now()

    const avgTime = (end - start) / iterations
    console.log(`数组权限平均每次调用时间: ${avgTime}ms`)

    // 期望每次调用时间小于 0.02ms
    expect(avgTime).toBeLessThan(0.02)
  })
})
